# 多人共享画板项目

基于Python实现的多人实时共享画板，采用客户端-服务器架构，自定义应用层网络协议。

## 项目特点

- 自定义TCP应用层协议
- 多线程服务器支持并发连接
- 实时同步绘图操作
- 用户认证和会话管理
- 轻量级GUI界面

## 项目结构

```
socket/
├── README.md
├── requirements.txt
├── environment.yml          # conda环境配置
├── src/
│   ├── __init__.py
│   ├── protocol/           # 网络协议模块
│   │   ├── __init__.py
│   │   ├── constants.py    # 协议常量定义
│   │   ├── message.py      # 消息格式和编解码
│   │   └── crypto.py       # 简单加密功能
│   ├── server/            # 服务器端网络处理
│   │   ├── __init__.py
│   │   ├── server.py      # 主服务器
│   │   └── client_handler.py  # 客户端连接处理
│   ├── client/            # 客户端网络处理
│   │   ├── __init__.py
│   │   └── client.py      # 网络客户端
│   ├── canvas/            # 画布核心逻辑
│   │   ├── __init__.py
│   │   ├── state.py       # 服务器端状态管理
│   │   ├── elements.py    # 绘图元素定义
│   │   └── widget.py      # 客户端画布组件
│   ├── gui/               # GUI界面模块
│   │   ├── __init__.py
│   │   ├── main_window.py # 主窗口
│   │   ├── dialogs.py     # 对话框组件
│   │   └── toolbar.py     # 工具栏组件
│   └── utils/             # 工具模块
│       ├── __init__.py
│       └── logger.py      # 日志工具
├── tests/                 # 测试模块
│   ├── __init__.py
│   ├── test_protocol.py
│   ├── test_server.py
│   └── test_client.py
├── scripts/               # 启动脚本
│   ├── start_server.py
│   └── start_client.py
└── docs/                  # 文档
    ├── protocol_design.md
    └── user_guide.md
```

## 环境设置

### 使用conda创建新环境（推荐）

```bash
# 创建新的conda环境
conda env create -f environment.yml

# 激活环境
conda activate shared-canvas

# 或者手动创建
conda create -n shared-canvas python=3.9
conda activate shared-canvas
pip install -r requirements.txt
```

### 使用pip安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

1. **启动服务器**
```bash
python scripts/start_server.py
```

2. **启动客户端**
```bash
python scripts/start_client.py
```

## 协议设计

本项目实现了自定义的TCP应用层协议，支持：
- 用户认证
- 实时绘图同步
- 多用户管理
- 心跳检测
- 基础加密

详细协议设计请参考 [protocol_design.md](docs/protocol_design.md)

## 测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_protocol.py -v
```
