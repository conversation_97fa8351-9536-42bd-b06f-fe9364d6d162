# 多人共享画板网络协议设计

## 1. 协议概述

本项目实现了一个基于TCP的自定义应用层协议，用于多人实时共享画板的通信。协议采用二进制消息头 + JSON载荷的混合格式，兼顾了效率和可扩展性。

## 2. 协议架构

### 2.1 通信模型
- **架构**: 客户端-服务器 (C/S) 架构
- **传输层**: TCP协议
- **连接模式**: 长连接，支持双向通信
- **并发模型**: 服务器端多线程处理

### 2.2 协议栈
```
应用层: 共享画板协议
传输层: TCP
网络层: IP
数据链路层: Ethernet
物理层: ...
```

## 3. 消息格式

### 3.1 消息结构
每条消息由固定长度的消息头和可变长度的消息体组成：

```
+----------+----------+----------+----------+
|  Version | MsgType  | PayloadLen| Reserved |
|  (1字节) | (2字节)  | (4字节)   | (1字节)  |
+----------+----------+----------+----------+
|                                           |
|            Payload (JSON)                 |
|          (PayloadLen 字节)                |
|                                           |
+-------------------------------------------+
```

### 3.2 消息头字段说明

| 字段 | 长度 | 描述 |
|------|------|------|
| Version | 1字节 | 协议版本号，当前为0x01 |
| MessageType | 2字节 | 消息类型，大端序 |
| PayloadLength | 4字节 | 载荷长度，大端序 |
| Reserved | 1字节 | 保留字段，用于未来扩展 |

### 3.3 载荷格式
载荷采用UTF-8编码的JSON格式，具有良好的可读性和扩展性。

## 4. 消息类型定义

| 代码 | 名称 | 方向 | 描述 |
|------|------|------|------|
| 0x0001 | AUTH_REQUEST | C→S | 客户端认证请求 |
| 0x0002 | AUTH_RESPONSE | S→C | 服务器认证响应 |
| 0x0003 | ADD_ELEMENT_REQUEST | C→S | 添加绘图元素请求 |
| 0x0004 | ELEMENT_ADDED_NOTIFY | S→C | 元素添加通知 |
| 0x0005 | DELETE_ELEMENT_REQUEST | C→S | 删除元素请求 |
| 0x0006 | ELEMENT_DELETED_NOTIFY | S→C | 元素删除通知 |
| 0x0007 | CLEAR_CANVAS_REQUEST | C→S | 清空画板请求 |
| 0x0008 | CANVAS_CLEARED_NOTIFY | S→C | 画板清空通知 |
| 0x0009 | USER_JOINED_NOTIFY | S→C | 用户加入通知 |
| 0x000A | USER_LEFT_NOTIFY | S→C | 用户离开通知 |
| 0x000B | HEARTBEAT_REQUEST | C→S | 心跳请求 |
| 0x000C | HEARTBEAT_RESPONSE | S→C | 心跳响应 |
| 0xFFFF | ERROR_NOTIFY | S→C | 错误通知 |

## 5. 消息载荷详细说明

### 5.1 认证相关

#### AUTH_REQUEST (0x0001)
```json
{
    "username": "用户名",
    "password_hash": "密码的SHA256哈希值"
}
```

#### AUTH_RESPONSE (0x0002)
```json
{
    "status": "success/failure",
    "user_id": "服务器分配的用户ID",
    "message": "响应消息",
    "canvas_state": [
        {
            "element_id": "元素ID",
            "user_id": "创建用户ID",
            "type": "元素类型",
            "attributes": {...}
        }
    ]
}
```

### 5.2 绘图元素相关

#### ADD_ELEMENT_REQUEST (0x0003)
```json
{
    "type": "line|rectangle|circle|freehand|text",
    "attributes": {
        // 根据元素类型的不同属性
    }
}
```

#### ELEMENT_ADDED_NOTIFY (0x0004)
```json
{
    "element_id": "服务器生成的元素ID",
    "user_id": "创建用户ID",
    "type": "元素类型",
    "attributes": {...}
}
```

### 5.3 元素属性定义

#### 直线 (line)
```json
{
    "startX": 起始X坐标,
    "startY": 起始Y坐标,
    "endX": 结束X坐标,
    "endY": 结束Y坐标,
    "color": "颜色值(#RRGGBB)",
    "thickness": 线条粗细
}
```

#### 矩形 (rectangle)
```json
{
    "x": X坐标,
    "y": Y坐标,
    "width": 宽度,
    "height": 高度,
    "strokeColor": "边框颜色",
    "fillColor": "填充颜色(可选)",
    "thickness": 边框粗细
}
```

#### 圆形 (circle)
```json
{
    "centerX": 圆心X坐标,
    "centerY": 圆心Y坐标,
    "radius": 半径,
    "strokeColor": "边框颜色",
    "fillColor": "填充颜色(可选)",
    "thickness": 边框粗细
}
```

#### 自由画笔 (freehand)
```json
{
    "points": [
        {"x": X坐标, "y": Y坐标},
        {"x": X坐标, "y": Y坐标},
        ...
    ],
    "color": "颜色值",
    "thickness": 线条粗细
}
```

#### 文本 (text)
```json
{
    "x": X坐标,
    "y": Y坐标,
    "content": "文本内容",
    "font_size": 字体大小,
    "color": "文本颜色"
}
```

## 6. 通信流程

### 6.1 连接建立与认证
```
客户端                    服务器
  |                        |
  |----TCP连接请求--------->|
  |<-------连接确认---------|
  |                        |
  |----AUTH_REQUEST------->|
  |                        |
  |<---AUTH_RESPONSE-------|
  |   (包含画板初始状态)     |
  |                        |
  |<---USER_JOINED_NOTIFY--|
  |   (广播给其他用户)       |
```

### 6.2 绘图操作同步
```
客户端A                   服务器                   客户端B
  |                        |                        |
  |--ADD_ELEMENT_REQUEST-->|                        |
  |                        |                        |
  |<--ELEMENT_ADDED_NOTIFY-|--ELEMENT_ADDED_NOTIFY->|
  |                        |                        |
```

### 6.3 心跳机制
```
客户端                    服务器
  |                        |
  |----HEARTBEAT_REQUEST-->|
  |<---HEARTBEAT_RESPONSE--|
  |                        |
  |     (每30秒一次)        |
```

## 7. 错误处理

### 7.1 错误代码
- 400: 无效请求
- 401: 未授权
- 404: 元素未找到
- 500: 服务器内部错误

### 7.2 错误响应格式
```json
{
    "code": 错误代码,
    "message": "错误描述"
}
```

## 8. 安全机制

### 8.1 认证安全
- 密码传输使用SHA256哈希
- 服务器端密码加盐存储
- 简单的用户名密码认证机制

### 8.2 连接安全
- TCP连接超时机制
- 心跳检测防止僵尸连接
- 异常断开自动清理

## 9. 协议特点

### 9.1 优点
- **高效**: 二进制消息头减少传输开销
- **可扩展**: JSON载荷易于添加新字段
- **可读**: JSON格式便于调试和分析
- **实时**: 基于TCP的长连接保证实时性
- **可靠**: TCP协议保证数据可靠传输

### 9.2 扩展性
- 版本号支持协议升级
- 保留字段用于未来扩展
- 消息类型可灵活添加
- JSON载荷支持向后兼容

## 10. 性能考虑

### 10.1 网络优化
- 消息头固定长度，解析高效
- 批量操作减少网络往返
- 心跳间隔平衡实时性和开销

### 10.2 并发处理
- 服务器多线程处理客户端
- 线程安全的状态管理
- 高效的消息广播机制
