# 多人共享画板用户指南

## 1. 项目简介

多人共享画板是一个基于Python实现的实时协作绘图工具，支持多个用户同时在同一画板上绘图，所有操作都会实时同步到其他用户。

### 1.1 主要功能
- 实时多人协作绘图
- 支持直线、矩形、圆形、自由画笔等绘图工具
- 多种颜色和画笔粗细选择
- 用户认证和会话管理
- 画板清空功能
- 自动断线重连

### 1.2 技术特点
- 自定义TCP应用层协议
- 客户端-服务器架构
- 多线程并发处理
- 轻量级GUI界面

## 2. 环境要求

### 2.1 系统要求
- Python 3.9 或更高版本
- 支持tkinter的操作系统（Windows、macOS、Linux）
- 网络连接（用于多人协作）

### 2.2 依赖库
项目主要使用Python标准库，无需额外安装第三方依赖：
- `tkinter` - GUI界面
- `socket` - 网络通信
- `threading` - 多线程处理
- `json` - 数据序列化

## 3. 安装和配置

### 3.1 使用conda环境（推荐）

```bash
# 克隆或下载项目
cd socket

# 创建conda环境
conda env create -f environment.yml

# 激活环境
conda activate shared-canvas
```

### 3.2 使用pip安装

```bash
# 安装测试依赖（可选）
pip install -r requirements.txt
```

### 3.3 验证安装

```bash
# 运行测试
python -m pytest tests/ -v
```

## 4. 快速开始

### 4.1 启动服务器

```bash
# 使用默认配置启动服务器
python scripts/start_server.py

# 或指定主机和端口
python scripts/start_server.py --host 0.0.0.0 --port 8888

# 启用调试模式
python scripts/start_server.py --debug
```

服务器启动后会显示：
```
Server started on localhost:8888
```

### 4.2 启动客户端

```bash
# 启动客户端GUI
python scripts/start_client.py
```

### 4.3 连接和认证

1. 点击工具栏中的"连接"按钮
2. 在弹出的对话框中输入：
   - 服务器地址（默认：localhost）
   - 端口（默认：8888）
   - 用户名（任意）
   - 密码（简化版：留空或输入用户名）
3. 点击"连接"按钮

认证成功后，状态栏会显示"已连接"状态。

## 5. 使用指南

### 5.1 绘图工具

#### 5.1.1 工具选择
- **直线**: 点击起点，拖拽到终点
- **矩形**: 点击一角，拖拽到对角
- **圆形**: 点击圆心，拖拽确定半径
- **画笔**: 自由绘制路径

#### 5.1.2 颜色选择
工具栏提供6种预设颜色：
- 黑色、红色、绿色、蓝色、黄色、紫色

#### 5.1.3 画笔粗细
可选择1、3、5、8像素的画笔粗细。

### 5.2 协作功能

#### 5.2.1 实时同步
- 所有绘图操作会实时同步到其他用户
- 其他用户的绘图会立即显示在本地画板

#### 5.2.2 用户管理
- 新用户加入时会收到通知
- 用户离开时会自动清理

#### 5.2.3 画板管理
- 点击"清空画板"可清除所有绘图内容
- 清空操作会同步到所有用户

### 5.3 界面说明

#### 5.3.1 菜单栏
- **文件**: 连接/断开服务器、退出
- **编辑**: 清空画板
- **帮助**: 关于信息

#### 5.3.2 工具栏
- 连接控制按钮
- 绘图工具选择
- 颜色和粗细设置
- 清空画板按钮

#### 5.3.3 状态栏
- 左侧：连接状态
- 右侧：在线用户数

## 6. 高级功能

### 6.1 服务器配置

#### 6.1.1 命令行参数
```bash
python scripts/start_server.py --help
```

可用参数：
- `--host`: 服务器监听地址
- `--port`: 服务器监听端口
- `--debug`: 启用调试日志

#### 6.1.2 配置文件
可以修改 `src/protocol/constants.py` 中的默认配置：
- `DEFAULT_SERVER_HOST`: 默认服务器地址
- `DEFAULT_SERVER_PORT`: 默认服务器端口
- `HEARTBEAT_INTERVAL`: 心跳间隔
- `CONNECTION_TIMEOUT`: 连接超时

### 6.2 网络配置

#### 6.2.1 局域网使用
1. 服务器启动时指定监听所有接口：
   ```bash
   python scripts/start_server.py --host 0.0.0.0
   ```
2. 客户端连接时输入服务器的实际IP地址

#### 6.2.2 防火墙设置
确保服务器端口（默认8888）在防火墙中开放。

## 7. 故障排除

### 7.1 常见问题

#### 7.1.1 连接失败
- 检查服务器是否正常运行
- 确认IP地址和端口正确
- 检查防火墙设置
- 确认网络连通性

#### 7.1.2 认证失败
- 检查用户名是否为空
- 简化版认证：密码留空或输入用户名

#### 7.1.3 绘图不同步
- 检查网络连接状态
- 查看服务器日志是否有错误
- 尝试重新连接

#### 7.1.4 GUI无法启动
- 确认系统支持tkinter
- 检查Python版本是否符合要求
- 在Linux上可能需要安装：`sudo apt-get install python3-tk`

### 7.2 日志调试

#### 7.2.1 启用调试日志
```bash
# 服务器调试
python scripts/start_server.py --debug

# 客户端调试（修改代码中的日志级别）
```

#### 7.2.2 查看日志
- 服务器日志会显示在控制台
- 客户端日志同样显示在控制台
- 可以重定向到文件保存

### 7.3 性能优化

#### 7.3.1 网络优化
- 使用有线网络连接
- 减少网络延迟
- 避免网络拥塞

#### 7.3.2 系统优化
- 关闭不必要的后台程序
- 确保足够的内存
- 使用SSD硬盘

## 8. 开发和扩展

### 8.1 代码结构
```
src/
├── protocol/     # 网络协议
├── server/       # 服务器端
├── client/       # 客户端
└── utils/        # 工具模块
```

### 8.2 添加新功能

#### 8.2.1 新绘图工具
1. 在 `ElementType` 中添加新类型
2. 在 `DrawingElement` 中实现绘制方法
3. 在 `CanvasWidget` 中添加交互逻辑

#### 8.2.2 新消息类型
1. 在 `MessageType` 中定义新类型
2. 在 `MessageBuilder` 中添加构建方法
3. 在服务器和客户端中添加处理逻辑

### 8.3 测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_protocol.py -v

# 生成覆盖率报告
python -m pytest tests/ --cov=src --cov-report=html
```

## 9. 许可证和贡献

本项目仅用于教学和学习目的，欢迎提出改进建议和bug报告。

### 9.1 已知限制
- 简化的用户认证机制
- 无持久化存储
- 基础的错误处理
- 有限的绘图工具

### 9.2 未来改进
- 更强的安全机制
- 数据库持久化
- 更多绘图工具
- 移动端支持
