"""
画板元素定义和绘制
定义各种绘图元素及其在画布上的绘制方法
"""

import tkinter as tk
from tkinter import Canvas
from typing import Dict, Any, Optional
from ..protocol.constants import ElementType
from ..utils.logger import get_logger


class DrawingElement:
    """绘图元素基类"""
    
    def __init__(self, element_id: str, user_id: str, element_type: str, 
                 attributes: Dict[str, Any], canvas_id: Optional[int] = None):
        self.element_id = element_id
        self.user_id = user_id
        self.type = element_type
        self.attributes = attributes
        self.canvas_id = canvas_id  # tkinter canvas对象ID
    
    def draw_on_canvas(self, canvas: Canvas) -> Optional[int]:
        """在画布上绘制元素"""
        if self.type == ElementType.LINE:
            return self._draw_line(canvas)
        elif self.type == ElementType.RECTANGLE:
            return self._draw_rectangle(canvas)
        elif self.type == ElementType.CIRCLE:
            return self._draw_circle(canvas)
        elif self.type == ElementType.FREEHAND:
            return self._draw_freehand(canvas)
        elif self.type == ElementType.TEXT:
            return self._draw_text(canvas)
        return None
    
    def _draw_line(self, canvas: Canvas) -> int:
        """绘制直线"""
        attrs = self.attributes
        return canvas.create_line(
            attrs.get('startX', 0), attrs.get('startY', 0),
            attrs.get('endX', 0), attrs.get('endY', 0),
            fill=attrs.get('color', '#000000'),
            width=attrs.get('thickness', 1)
        )
    
    def _draw_rectangle(self, canvas: Canvas) -> int:
        """绘制矩形"""
        attrs = self.attributes
        x, y = attrs.get('x', 0), attrs.get('y', 0)
        width, height = attrs.get('width', 0), attrs.get('height', 0)
        
        return canvas.create_rectangle(
            x, y, x + width, y + height,
            outline=attrs.get('strokeColor', '#000000'),
            fill=attrs.get('fillColor', ''),
            width=attrs.get('thickness', 1)
        )
    
    def _draw_circle(self, canvas: Canvas) -> int:
        """绘制圆形"""
        attrs = self.attributes
        cx, cy = attrs.get('centerX', 0), attrs.get('centerY', 0)
        radius = attrs.get('radius', 0)
        
        return canvas.create_oval(
            cx - radius, cy - radius, cx + radius, cy + radius,
            outline=attrs.get('strokeColor', '#000000'),
            fill=attrs.get('fillColor', ''),
            width=attrs.get('thickness', 1)
        )
    
    def _draw_freehand(self, canvas: Canvas) -> Optional[int]:
        """绘制自由画笔"""
        attrs = self.attributes
        points = attrs.get('points', [])
        
        if len(points) < 2:
            return None
        
        # 将点列表转换为坐标列表
        coords = []
        for point in points:
            coords.extend([point.get('x', 0), point.get('y', 0)])
        
        return canvas.create_line(
            coords,
            fill=attrs.get('color', '#000000'),
            width=attrs.get('thickness', 1),
            smooth=True
        )
    
    def _draw_text(self, canvas: Canvas) -> int:
        """绘制文本"""
        attrs = self.attributes
        return canvas.create_text(
            attrs.get('x', 0), attrs.get('y', 0),
            text=attrs.get('content', ''),
            fill=attrs.get('color', '#000000'),
            font=('Arial', attrs.get('font_size', 12))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于网络传输）"""
        return {
            "element_id": self.element_id,
            "user_id": self.user_id,
            "type": self.type,
            "attributes": self.attributes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DrawingElement':
        """从字典创建元素"""
        return cls(
            data["element_id"],
            data["user_id"],
            data["type"],
            data["attributes"]
        )
    
    def __str__(self) -> str:
        return f"DrawingElement(id={self.element_id}, type={self.type}, user={self.user_id})"
    
    def __repr__(self) -> str:
        return self.__str__()
