"""
画板状态管理
维护画板的完整状态和元素管理（服务器端）
"""

import threading
import uuid
from typing import Dict, List, Any, Optional
from ..utils.logger import get_logger


class CanvasElement:
    """画板元素类"""
    
    def __init__(self, element_id: str, user_id: str, element_type: str, 
                 attributes: Dict[str, Any]):
        self.element_id = element_id
        self.user_id = user_id
        self.type = element_type
        self.attributes = attributes
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "element_id": self.element_id,
            "user_id": self.user_id,
            "type": self.type,
            "attributes": self.attributes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CanvasElement':
        """从字典创建元素"""
        return cls(
            data["element_id"],
            data["user_id"],
            data["type"],
            data["attributes"]
        )


class CanvasState:
    """画板状态管理器（服务器端）"""
    
    def __init__(self):
        self.elements: Dict[str, CanvasElement] = {}
        self.lock = threading.RLock()
        self.logger = get_logger('CanvasState')
    
    def add_element(self, user_id: str, element_type: str, 
                   attributes: Dict[str, Any]) -> str:
        """
        添加新元素
        
        Args:
            user_id: 用户ID
            element_type: 元素类型
            attributes: 元素属性
        
        Returns:
            str: 生成的元素ID
        """
        with self.lock:
            element_id = str(uuid.uuid4())
            element = CanvasElement(element_id, user_id, element_type, attributes)
            self.elements[element_id] = element
            
            self.logger.info(f"Added element {element_id} by user {user_id}")
            return element_id
    
    def remove_element(self, element_id: str) -> bool:
        """
        删除元素
        
        Args:
            element_id: 元素ID
        
        Returns:
            bool: 是否成功删除
        """
        with self.lock:
            if element_id in self.elements:
                del self.elements[element_id]
                self.logger.info(f"Removed element {element_id}")
                return True
            return False
    
    def clear_canvas(self):
        """清空画板"""
        with self.lock:
            element_count = len(self.elements)
            self.elements.clear()
            self.logger.info(f"Cleared canvas, removed {element_count} elements")
    
    def get_element(self, element_id: str) -> Optional[CanvasElement]:
        """获取指定元素"""
        with self.lock:
            return self.elements.get(element_id)
    
    def get_all_elements(self) -> List[Dict[str, Any]]:
        """获取所有元素的字典表示"""
        with self.lock:
            return [element.to_dict() for element in self.elements.values()]
    
    def get_elements_by_user(self, user_id: str) -> List[CanvasElement]:
        """获取指定用户的所有元素"""
        with self.lock:
            return [element for element in self.elements.values() 
                   if element.user_id == user_id]
    
    def get_element_count(self) -> int:
        """获取元素总数"""
        with self.lock:
            return len(self.elements)
    
    def __str__(self) -> str:
        return f"CanvasState(elements={len(self.elements)})"
