"""
画板组件
提供交互式画板界面，处理用户绘图操作
"""

import tkinter as tk
from tkinter import Canvas
from typing import Dict, Any, Optional, Callable
from .elements import DrawingElement
from ..protocol.constants import ElementType, DEFAULT_THICKNESS
from ..utils.logger import get_logger


class CanvasWidget:
    """交互式画板组件"""

    def __init__(self, parent, width: int = 800, height: int = 600):
        self.parent = parent
        self.width = width
        self.height = height

        # 创建画布
        self.canvas = Canvas(parent, width=width, height=height, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 元素管理
        self.elements: Dict[str, DrawingElement] = {}

        # 绘图状态
        self.current_tool = ElementType.LINE
        self.current_color = "#000000"  # 默认黑色
        self.current_thickness = DEFAULT_THICKNESS[0]

        # 绘图临时状态
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.temp_item = None
        self.freehand_points = []

        # 回调函数
        self.element_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None

        self.logger = get_logger("CanvasWidget")

        # 绑定鼠标事件
        self._bind_events()

    def _bind_events(self):
        """绑定鼠标事件"""
        self.canvas.bind("<Button-1>", self._on_mouse_down)
        self.canvas.bind("<B1-Motion>", self._on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_mouse_up)

    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y

        if self.current_tool == ElementType.FREEHAND:
            self.freehand_points = [{"x": event.x, "y": event.y}]

    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.drawing:
            return

        if self.current_tool == ElementType.LINE:
            self._preview_line(event.x, event.y)
        elif self.current_tool == ElementType.RECTANGLE:
            self._preview_rectangle(event.x, event.y)
        elif self.current_tool == ElementType.CIRCLE:
            self._preview_circle(event.x, event.y)
        elif self.current_tool == ElementType.FREEHAND:
            self.freehand_points.append({"x": event.x, "y": event.y})
            self._preview_freehand()

    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        if not self.drawing:
            return

        self.drawing = False

        # 清除预览
        if self.temp_item:
            self.canvas.delete(self.temp_item)
            self.temp_item = None

        # 创建最终元素
        if self.current_tool == ElementType.LINE:
            self._create_line(event.x, event.y)
        elif self.current_tool == ElementType.RECTANGLE:
            self._create_rectangle(event.x, event.y)
        elif self.current_tool == ElementType.CIRCLE:
            self._create_circle(event.x, event.y)
        elif self.current_tool == ElementType.FREEHAND:
            self.freehand_points.append({"x": event.x, "y": event.y})
            self._create_freehand()

    def _preview_line(self, end_x: int, end_y: int):
        """预览直线"""
        if self.temp_item:
            self.canvas.delete(self.temp_item)

        self.temp_item = self.canvas.create_line(
            self.start_x,
            self.start_y,
            end_x,
            end_y,
            fill=self.current_color,
            width=self.current_thickness,
        )

    def _preview_rectangle(self, end_x: int, end_y: int):
        """预览矩形"""
        if self.temp_item:
            self.canvas.delete(self.temp_item)

        self.temp_item = self.canvas.create_rectangle(
            self.start_x,
            self.start_y,
            end_x,
            end_y,
            outline=self.current_color,
            width=self.current_thickness,
        )

    def _preview_circle(self, end_x: int, end_y: int):
        """预览圆形"""
        if self.temp_item:
            self.canvas.delete(self.temp_item)

        # 计算半径
        radius = ((end_x - self.start_x) ** 2 + (end_y - self.start_y) ** 2) ** 0.5

        self.temp_item = self.canvas.create_oval(
            self.start_x - radius,
            self.start_y - radius,
            self.start_x + radius,
            self.start_y + radius,
            outline=self.current_color,
            width=self.current_thickness,
        )

    def _preview_freehand(self):
        """预览自由画笔"""
        if self.temp_item:
            self.canvas.delete(self.temp_item)

        if len(self.freehand_points) < 2:
            return

        coords = []
        for point in self.freehand_points:
            coords.extend([point["x"], point["y"]])

        self.temp_item = self.canvas.create_line(
            coords, fill=self.current_color, width=self.current_thickness, smooth=True
        )

    def _create_line(self, end_x: int, end_y: int):
        """创建直线元素"""
        attributes = {
            "startX": self.start_x,
            "startY": self.start_y,
            "endX": end_x,
            "endY": end_y,
            "color": self.current_color,
            "thickness": self.current_thickness,
        }
        self._emit_element(ElementType.LINE, attributes)

    def _create_rectangle(self, end_x: int, end_y: int):
        """创建矩形元素"""
        x = min(self.start_x, end_x)
        y = min(self.start_y, end_y)
        width = abs(end_x - self.start_x)
        height = abs(end_y - self.start_y)

        attributes = {
            "x": x,
            "y": y,
            "width": width,
            "height": height,
            "strokeColor": self.current_color,
            "thickness": self.current_thickness,
        }
        self._emit_element(ElementType.RECTANGLE, attributes)

    def _create_circle(self, end_x: int, end_y: int):
        """创建圆形元素"""
        radius = ((end_x - self.start_x) ** 2 + (end_y - self.start_y) ** 2) ** 0.5

        attributes = {
            "centerX": self.start_x,
            "centerY": self.start_y,
            "radius": radius,
            "strokeColor": self.current_color,
            "thickness": self.current_thickness,
        }
        self._emit_element(ElementType.CIRCLE, attributes)

    def _create_freehand(self):
        """创建自由画笔元素"""
        if len(self.freehand_points) < 2:
            return

        attributes = {
            "points": self.freehand_points,
            "color": self.current_color,
            "thickness": self.current_thickness,
        }
        self._emit_element(ElementType.FREEHAND, attributes)

    def _emit_element(self, element_type: str, attributes: Dict[str, Any]):
        """发送元素创建事件"""
        if self.element_callback:
            self.element_callback(element_type, attributes)

    def add_element(
        self,
        element_id: str,
        user_id: str,
        element_type: str,
        attributes: Dict[str, Any],
    ):
        """添加远程元素"""
        element = DrawingElement(element_id, user_id, element_type, attributes)
        canvas_id = element.draw_on_canvas(self.canvas)
        element.canvas_id = canvas_id

        self.elements[element_id] = element
        self.logger.debug(f"Added element {element_id}")

    def remove_element(self, element_id: str):
        """移除元素"""
        if element_id in self.elements:
            element = self.elements[element_id]
            if element.canvas_id:
                self.canvas.delete(element.canvas_id)
            del self.elements[element_id]
            self.logger.debug(f"Removed element {element_id}")

    def clear_canvas(self):
        """清空画板"""
        self.canvas.delete("all")
        self.elements.clear()
        self.logger.info("Canvas cleared")

    def set_tool(self, tool: str):
        """设置当前工具"""
        self.current_tool = tool

    def set_color(self, color: str):
        """设置当前颜色"""
        self.current_color = color

    def set_thickness(self, thickness: int):
        """设置当前画笔粗细"""
        self.current_thickness = thickness

    def set_element_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """设置元素创建回调"""
        self.element_callback = callback

    def get_element_count(self) -> int:
        """获取元素数量"""
        return len(self.elements)

    def get_canvas_size(self) -> tuple[int, int]:
        """获取画布尺寸"""
        return self.width, self.height
