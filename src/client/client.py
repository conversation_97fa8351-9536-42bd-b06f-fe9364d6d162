"""
网络客户端
处理与服务器的网络通信
"""

import socket
import threading
import time
from typing import Optional, Callable, Dict, Any
from ..protocol.message import Message, MessageBuilder
from ..protocol.constants import (
    MessageType,
    DEFAULT_SERVER_HOST,
    DEFAULT_SERVER_PORT,
    HEADER_LENGTH,
    HEARTBEAT_INTERVAL,
)
from ..protocol.crypto import simple_hash
from ..utils.logger import get_logger


class NetworkClient:
    """网络客户端"""

    def __init__(self, message_callback: Callable[[Message], None] = None):
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.authenticated = False
        self.running = False

        self.user_id: Optional[str] = None
        self.username: Optional[str] = None

        # 消息处理回调
        self.message_callback = message_callback

        # 接收线程
        self.receive_thread: Optional[threading.Thread] = None
        self.heartbeat_thread: Optional[threading.Thread] = None

        self.logger = get_logger("NetworkClient")

    def connect(
        self, host: str = DEFAULT_SERVER_HOST, port: int = DEFAULT_SERVER_PORT
    ) -> bool:
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10.0)  # 连接超时
            self.socket.connect((host, port))

            self.connected = True
            self.running = True

            # 启动接收线程
            self.receive_thread = threading.Thread(
                target=self._receive_loop, daemon=True
            )
            self.receive_thread.start()

            self.logger.info(f"Connected to server {host}:{port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to server: {e}")
            return False

    def authenticate(self, username: str, password: str) -> bool:
        """用户认证"""
        if not self.connected:
            self.logger.error("Not connected to server")
            return False

        try:
            # 计算密码哈希
            password_hash = simple_hash(password)

            # 发送认证请求
            auth_message = MessageBuilder.auth_request(username, password_hash)
            self.send_message(auth_message)

            self.username = username
            self.logger.info(f"Sent authentication request for user: {username}")
            return True

        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            return False

    def register(self, username: str, password: str) -> bool:
        """用户注册"""
        if not self.connected:
            self.logger.error("Not connected to server")
            return False

        try:
            # 计算密码哈希
            password_hash = simple_hash(password)

            # 发送注册请求
            register_message = MessageBuilder.register_request(username, password_hash)
            self.send_message(register_message)

            self.logger.info(f"Sent registration request for user: {username}")
            return True

        except Exception as e:
            self.logger.error(f"Registration failed: {e}")
            return False

    def send_message(self, message: Message) -> bool:
        """发送消息到服务器"""
        if not self.connected or not self.socket:
            return False

        try:
            data = message.encode()
            self.socket.sendall(data)
            self.logger.debug(f"Sent message: {message}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            self.disconnect()
            return False

    def add_element(self, element_type: str, attributes: Dict[str, Any]) -> bool:
        """添加绘图元素"""
        if not self.authenticated:
            return False

        message = MessageBuilder.add_element_request(element_type, attributes)
        return self.send_message(message)

    def clear_canvas(self) -> bool:
        """清空画板"""
        if not self.authenticated:
            return False

        message = MessageBuilder.clear_canvas_request()
        return self.send_message(message)

    def _receive_loop(self):
        """接收消息循环"""
        while self.running and self.connected:
            try:
                message = self._receive_message()
                if message is None:
                    break

                self.logger.debug(f"Received message: {message}")
                self._handle_message(message)

            except Exception as e:
                if self.running:
                    self.logger.error(f"Error in receive loop: {e}")
                break

        self.disconnect()

    def _receive_message(self) -> Optional[Message]:
        """接收完整消息"""
        try:
            # 接收消息头
            header_data = self._receive_exact(HEADER_LENGTH)
            if not header_data:
                return None

            # 解析消息头获取payload长度
            _, _, payload_length, _ = Message.decode_header(header_data)

            # 接收payload
            payload_data = b""
            if payload_length > 0:
                payload_data = self._receive_exact(payload_length)
                if not payload_data:
                    return None

            # 解码完整消息
            full_data = header_data + payload_data
            return Message.decode(full_data)

        except Exception as e:
            self.logger.error(f"Error receiving message: {e}")
            return None

    def _receive_exact(self, length: int) -> Optional[bytes]:
        """接收指定长度的数据"""
        data = b""
        while len(data) < length:
            try:
                chunk = self.socket.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.error:
                return None
        return data

    def _handle_message(self, message: Message):
        """处理接收到的消息"""
        if message.message_type == MessageType.AUTH_RESPONSE:
            self._handle_auth_response(message)
        elif message.message_type == MessageType.REGISTER_RESPONSE:
            self._handle_register_response(message)
        elif message.message_type == MessageType.HEARTBEAT_REQUEST:
            self._handle_heartbeat_request(message)
        else:
            # 其他消息转发给回调函数
            if self.message_callback:
                self.message_callback(message)

    def _handle_auth_response(self, message: Message):
        """处理认证响应"""
        status = message.payload.get("status")
        if status == "success":
            self.authenticated = True
            self.user_id = message.payload.get("user_id")

            # 启动心跳线程
            self.heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop, daemon=True
            )
            self.heartbeat_thread.start()

            self.logger.info(f"Authentication successful, user_id: {self.user_id}")
        else:
            error_msg = message.payload.get("message", "Authentication failed")
            self.logger.error(f"Authentication failed: {error_msg}")

        # 转发认证响应给回调
        if self.message_callback:
            self.message_callback(message)

    def _handle_register_response(self, message: Message):
        """处理注册响应"""
        success = message.payload.get("success")
        message_text = message.payload.get("message", "")

        if success:
            self.logger.info(f"Registration successful: {message_text}")
        else:
            self.logger.error(f"Registration failed: {message_text}")

        # 转发注册响应给回调
        if self.message_callback:
            self.message_callback(message)

    def _handle_heartbeat_request(self, message: Message):
        """处理心跳请求"""
        response = MessageBuilder.heartbeat_response()
        self.send_message(response)

    def _heartbeat_loop(self):
        """心跳循环"""
        while self.running and self.authenticated:
            try:
                time.sleep(HEARTBEAT_INTERVAL)
                if self.running and self.authenticated:
                    heartbeat = MessageBuilder.heartbeat_request()
                    self.send_message(heartbeat)
            except Exception as e:
                self.logger.error(f"Heartbeat error: {e}")
                break

    def disconnect(self):
        """断开连接"""
        self.running = False
        self.connected = False
        self.authenticated = False

        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

        self.logger.info("Disconnected from server")

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connected

    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.authenticated
