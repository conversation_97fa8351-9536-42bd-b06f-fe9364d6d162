"""
用户认证对话框
包含登录和注册功能的统一对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any
from ..protocol.constants import DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT


class AuthDialog:
    """用户认证对话框（登录/注册）"""
    
    def __init__(self, parent):
        self.result: Optional[Dict[str, Any]] = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("用户认证")
        self.dialog.geometry("380x320")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="连接到共享画板服务器", font=("Arial", 12, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))
        
        # 服务器配置
        server_frame = ttk.LabelFrame(main_frame, text="服务器配置", padding="10")
        server_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        ttk.Label(server_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.host_var = tk.StringVar(value=DEFAULT_SERVER_HOST)
        ttk.Entry(server_frame, textvariable=self.host_var, width=25).grid(row=0, column=1, pady=2, padx=(10, 0))
        
        ttk.Label(server_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.port_var = tk.StringVar(value=str(DEFAULT_SERVER_PORT))
        ttk.Entry(server_frame, textvariable=self.port_var, width=25).grid(row=1, column=1, pady=2, padx=(10, 0))
        
        # 用户认证
        auth_frame = ttk.LabelFrame(main_frame, text="用户认证", padding="10")
        auth_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # 模式选择
        mode_frame = ttk.Frame(auth_frame)
        mode_frame.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        self.mode_var = tk.StringVar(value="login")
        ttk.Radiobutton(mode_frame, text="登录", variable=self.mode_var, value="login", 
                       command=self._on_mode_changed).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="注册新用户", variable=self.mode_var, value="register", 
                       command=self._on_mode_changed).pack(side=tk.LEFT)
        
        # 用户名
        ttk.Label(auth_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(auth_frame, textvariable=self.username_var, width=25)
        self.username_entry.grid(row=1, column=1, pady=2, padx=(10, 0))
        
        # 密码
        ttk.Label(auth_frame, text="密码:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(auth_frame, textvariable=self.password_var, width=25, show="*")
        self.password_entry.grid(row=2, column=1, pady=2, padx=(10, 0))
        
        # 确认密码（注册时显示）
        self.confirm_label = ttk.Label(auth_frame, text="确认密码:")
        self.confirm_password_var = tk.StringVar()
        self.confirm_password_entry = ttk.Entry(auth_frame, textvariable=self.confirm_password_var, width=25, show="*")
        
        # 提示信息
        self.hint_label = ttk.Label(main_frame, text="", font=("Arial", 9), foreground="gray")
        self.hint_label.grid(row=3, column=0, columnspan=2, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=15)
        
        self.action_button = ttk.Button(button_frame, text="连接", command=self._on_action)
        self.action_button.pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self._on_action())
        
        # 初始化界面状态
        self._on_mode_changed()
        
        # 设置焦点
        self.username_entry.focus_set()
    
    def _on_mode_changed(self):
        """模式改变处理"""
        mode = self.mode_var.get()
        
        if mode == "login":
            # 登录模式
            self.action_button.config(text="连接")
            self.dialog.title("登录到服务器")
            
            # 隐藏确认密码字段
            self.confirm_label.grid_remove()
            self.confirm_password_entry.grid_remove()
            
            # 更新提示信息
            self.hint_label.config(text="登录模式：密码可留空（将使用用户名作为密码）")
            
        else:
            # 注册模式
            self.action_button.config(text="注册并连接")
            self.dialog.title("注册新用户")
            
            # 显示确认密码字段
            self.confirm_label.grid(row=3, column=0, sticky=tk.W, pady=2)
            self.confirm_password_entry.grid(row=3, column=1, pady=2, padx=(10, 0))
            
            # 更新提示信息
            self.hint_label.config(text="注册模式：请设置密码，密码不能为空")
    
    def _on_action(self):
        """执行登录或注册操作"""
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get()
        mode = self.mode_var.get()
        
        # 基本验证
        if not all([host, port_str, username]):
            messagebox.showerror("错误", "请填写所有必填字段")
            return
        
        try:
            port = int(port_str)
        except ValueError:
            messagebox.showerror("错误", "端口必须是数字")
            return
        
        if mode == "login":
            # 登录模式：密码为空时使用用户名作为密码
            if not password:
                password = username
        else:
            # 注册模式：密码验证
            if not password:
                messagebox.showerror("错误", "注册时密码不能为空")
                return
            
            confirm_password = self.confirm_password_var.get()
            if password != confirm_password:
                messagebox.showerror("错误", "两次输入的密码不一致")
                return
            
            if len(password) < 3:
                messagebox.showerror("错误", "密码长度至少3个字符")
                return
        
        self.result = {
            'host': host,
            'port': port,
            'username': username,
            'password': password,
            'mode': mode
        }
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消操作"""
        self.dialog.destroy()
    
    def show(self) -> Optional[Dict[str, Any]]:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


# 保持向后兼容的简单登录对话框
class LoginDialog(AuthDialog):
    """简单登录对话框（向后兼容）"""
    
    def __init__(self, parent):
        super().__init__(parent)
        # 默认设置为登录模式并隐藏模式选择
        self.mode_var.set("login")
        # 可以在这里隐藏模式选择控件，但为了功能完整性，保留它们


class RegisterDialog(AuthDialog):
    """注册对话框"""
    
    def __init__(self, parent):
        super().__init__(parent)
        # 默认设置为注册模式
        self.mode_var.set("register")
        self._on_mode_changed()


def show_error(parent, title: str, message: str):
    """显示错误对话框"""
    messagebox.showerror(title, message, parent=parent)


def show_info(parent, title: str, message: str):
    """显示信息对话框"""
    messagebox.showinfo(title, message, parent=parent)


def show_warning(parent, title: str, message: str):
    """显示警告对话框"""
    messagebox.showwarning(title, message, parent=parent)


def ask_yes_no(parent, title: str, message: str) -> bool:
    """显示是否确认对话框"""
    return messagebox.askyesno(title, message, parent=parent)
