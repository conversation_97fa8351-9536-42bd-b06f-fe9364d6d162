"""
GUI界面常量定义
包含颜色、尺寸、样式等GUI相关的常量
"""

# 颜色预设
DEFAULT_COLORS = [
    "#000000",  # 黑色
    "#FF0000",  # 红色
    "#00FF00",  # 绿色
    "#0000FF",  # 蓝色
    "#FFFF00",  # 黄色
    "#FF00FF",  # 紫色
    "#00FFFF",  # 青色
    "#FFFFFF",  # 白色
]

# 颜色名称映射（用于显示）
COLOR_NAMES = {
    "#000000": "黑色",
    "#FF0000": "红色", 
    "#00FF00": "绿色",
    "#0000FF": "蓝色",
    "#FFFF00": "黄色",
    "#FF00FF": "紫色",
    "#00FFFF": "青色",
    "#FFFFFF": "白色",
}

# 画笔粗细预设
DEFAULT_THICKNESS = [1, 3, 5, 8]

# 画笔粗细名称映射
THICKNESS_NAMES = {
    1: "细",
    3: "中",
    5: "粗", 
    8: "特粗"
}

# 窗口尺寸
DEFAULT_WINDOW_WIDTH = 1000
DEFAULT_WINDOW_HEIGHT = 700
DEFAULT_CANVAS_WIDTH = 800
DEFAULT_CANVAS_HEIGHT = 600

# 工具栏配置
TOOLBAR_BUTTON_PADDING = 2
COLOR_BUTTON_SIZE = 2
THICKNESS_COMBO_WIDTH = 8

# 对话框尺寸
LOGIN_DIALOG_WIDTH = 350
LOGIN_DIALOG_HEIGHT = 250
ABOUT_DIALOG_WIDTH = 400
ABOUT_DIALOG_HEIGHT = 350

# 状态栏配置
STATUS_UPDATE_INTERVAL = 1000  # 毫秒

# 颜色主题
CANVAS_BACKGROUND = "white"
TOOLBAR_BACKGROUND = "lightgray"
STATUS_BAR_BACKGROUND = "lightgray"

# 字体配置
DEFAULT_FONT_FAMILY = "Arial"
DEFAULT_FONT_SIZE = 12
TITLE_FONT_SIZE = 16
BUTTON_FONT_SIZE = 10
