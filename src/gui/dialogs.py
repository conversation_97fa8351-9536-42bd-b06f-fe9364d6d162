"""
GUI对话框组件
包含各种对话框的实现
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any
from ..protocol.constants import DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT


class LoginDialog:
    """登录对话框"""

    def __init__(self, parent):
        self.result: Optional[Dict[str, Any]] = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("连接到服务器")
        self.dialog.geometry("350x280")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry(
            "+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50)
        )

        self._create_widgets()

    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 服务器地址
        ttk.Label(main_frame, text="服务器地址:").grid(
            row=0, column=0, sticky=tk.W, pady=2
        )
        self.host_var = tk.StringVar(value=DEFAULT_SERVER_HOST)
        ttk.Entry(main_frame, textvariable=self.host_var, width=20).grid(
            row=0, column=1, pady=2
        )

        # 端口
        ttk.Label(main_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.port_var = tk.StringVar(value=str(DEFAULT_SERVER_PORT))
        ttk.Entry(main_frame, textvariable=self.port_var, width=20).grid(
            row=1, column=1, pady=2
        )

        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.username_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.username_var, width=20).grid(
            row=2, column=1, pady=2
        )

        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.password_var, width=20, show="*").grid(
            row=3, column=1, pady=2
        )

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="连接", command=self._on_connect).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(
            side=tk.LEFT, padx=5
        )

        # 绑定回车键
        self.dialog.bind("<Return>", lambda e: self._on_connect())

        # 设置焦点到用户名输入框
        self.username_var.trace("w", self._update_password_hint)

    def _update_password_hint(self, *args):
        """更新密码提示"""
        # 简单提示：密码就是用户名（简化版认证）
        pass

    def _on_connect(self):
        """连接按钮点击"""
        host = self.host_var.get().strip()
        port_str = self.port_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get()

        if not all([host, port_str, username]):
            messagebox.showerror("错误", "请填写所有必填字段")
            return

        try:
            port = int(port_str)
        except ValueError:
            messagebox.showerror("错误", "端口必须是数字")
            return

        # 简单验证：密码为空时使用用户名作为密码
        if not password:
            password = username

        self.result = {
            "host": host,
            "port": port,
            "username": username,
            "password": password,
        }
        self.dialog.destroy()

    def _on_cancel(self):
        """取消按钮点击"""
        self.dialog.destroy()

    def show(self) -> Optional[Dict[str, Any]]:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class AboutDialog:
    """关于对话框"""

    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("关于")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry(
            "+%d+%d" % (parent.winfo_rootx() + 100, parent.winfo_rooty() + 100)
        )

        self._create_widgets()

    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(
            main_frame, text="多人共享画板", font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 10))

        # 版本信息
        version_label = ttk.Label(main_frame, text="版本 1.0.0", font=("Arial", 12))
        version_label.pack(pady=(0, 20))

        # 描述
        description = """基于Python实现的实时协作绘图工具

主要特性：
• 多用户实时协作绘图
• 自定义TCP应用层协议
• 支持直线、矩形、圆形、自由画笔
• 轻量级GUI界面
• 跨平台支持

技术栈：
• Python 3.9+
• tkinter GUI
• 多线程网络编程
• 自定义协议设计"""

        desc_label = ttk.Label(main_frame, text=description, justify=tk.LEFT)
        desc_label.pack(pady=(0, 20))

        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=self.dialog.destroy).pack()

    def show(self):
        """显示对话框"""
        self.dialog.wait_window()


def show_error(parent, title: str, message: str):
    """显示错误对话框"""
    messagebox.showerror(title, message, parent=parent)


def show_info(parent, title: str, message: str):
    """显示信息对话框"""
    messagebox.showinfo(title, message, parent=parent)


def show_warning(parent, title: str, message: str):
    """显示警告对话框"""
    messagebox.showwarning(title, message, parent=parent)


def ask_yes_no(parent, title: str, message: str) -> bool:
    """显示是否确认对话框"""
    return messagebox.askyesno(title, message, parent=parent)
