"""
主窗口GUI
实现主界面和消息处理逻辑
"""

import tkinter as tk
from tkinter import ttk
import threading
from typing import Optional

from .dialogs import LoginDialog, AboutDialog, ask_yes_no, show_error
from .toolbar import Toolbar
from ..client.client import NetworkClient
from ..canvas.widget import CanvasWidget
from ..protocol.message import Message
from ..protocol.constants import MessageType
from ..utils.logger import get_logger


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("多人共享画板")
        self.root.geometry("1000x700")
        
        # 网络客户端
        self.client: Optional[NetworkClient] = None
        
        # GUI组件
        self.toolbar: Optional[Toolbar] = None
        self.canvas_widget: Optional[CanvasWidget] = None
        self.status_var = tk.StringVar(value="未连接")
        self.users_var = tk.StringVar(value="用户: 0")
        
        self.logger = get_logger('MainWindow')
        
        self._create_widgets()
        self._setup_menu()
        self._setup_callbacks()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        self.toolbar = Toolbar(main_frame)
        self.toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 画板区域
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.canvas_widget = CanvasWidget(canvas_frame)
        
        # 状态栏
        self._create_statusbar(main_frame)
    
    def _create_statusbar(self, parent):
        """创建状态栏"""
        statusbar = ttk.Frame(parent)
        statusbar.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(statusbar, textvariable=self.status_var).pack(side=tk.LEFT)
        ttk.Label(statusbar, textvariable=self.users_var).pack(side=tk.RIGHT)
    
    def _setup_menu(self):
        """设置菜单"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="连接服务器", command=self._connect_to_server)
        file_menu.add_command(label="断开连接", command=self._disconnect_from_server)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="清空画板", command=self._clear_canvas)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 工具栏回调
        self.toolbar.set_connect_callback(self._connect_to_server)
        self.toolbar.set_disconnect_callback(self._disconnect_from_server)
        self.toolbar.set_tool_changed_callback(self._on_tool_changed)
        self.toolbar.set_color_changed_callback(self._on_color_changed)
        self.toolbar.set_thickness_changed_callback(self._on_thickness_changed)
        self.toolbar.set_clear_canvas_callback(self._clear_canvas)
        
        # 画板回调
        self.canvas_widget.set_element_callback(self._on_element_created)
    
    def _connect_to_server(self):
        """连接到服务器"""
        if self.client and self.client.is_connected():
            show_error(self.root, "提示", "已经连接到服务器")
            return
        
        # 显示登录对话框
        login_dialog = LoginDialog(self.root)
        result = login_dialog.show()
        
        if not result:
            return
        
        # 在后台线程中连接
        def connect_thread():
            try:
                self.client = NetworkClient(self._on_message_received)
                
                self.status_var.set("正在连接...")
                
                if self.client.connect(result['host'], result['port']):
                    if self.client.authenticate(result['username'], result['password']):
                        self.status_var.set(f"已连接到 {result['host']}:{result['port']}")
                    else:
                        self.status_var.set("认证失败")
                        self.client.disconnect()
                        self.client = None
                else:
                    self.status_var.set("连接失败")
                    self.client = None
                    
            except Exception as e:
                self.logger.error(f"Connection error: {e}")
                self.status_var.set("连接错误")
                if self.client:
                    self.client.disconnect()
                    self.client = None
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def _disconnect_from_server(self):
        """断开服务器连接"""
        if self.client:
            self.client.disconnect()
            self.client = None
        
        self.status_var.set("未连接")
        self.users_var.set("用户: 0")
    
    def _on_element_created(self, element_type: str, attributes: dict):
        """元素创建回调"""
        if self.client and self.client.is_authenticated():
            self.client.add_element(element_type, attributes)
    
    def _on_tool_changed(self, tool: str):
        """工具改变回调"""
        if self.canvas_widget:
            self.canvas_widget.set_tool(tool)
    
    def _on_color_changed(self, color: str):
        """颜色改变回调"""
        if self.canvas_widget:
            self.canvas_widget.set_color(color)
    
    def _on_thickness_changed(self, thickness: int):
        """画笔粗细改变回调"""
        if self.canvas_widget:
            self.canvas_widget.set_thickness(thickness)
    
    def _clear_canvas(self):
        """清空画板"""
        if self.client and self.client.is_authenticated():
            result = ask_yes_no(self.root, "确认", "确定要清空画板吗？这将删除所有绘图内容。")
            if result:
                self.client.clear_canvas()
    
    def _show_about(self):
        """显示关于对话框"""
        about_dialog = AboutDialog(self.root)
        about_dialog.show()
    
    def _on_message_received(self, message: Message):
        """消息接收回调"""
        # 在主线程中处理GUI更新
        self.root.after(0, lambda: self._handle_message(message))
    
    def _handle_message(self, message: Message):
        """处理接收到的消息"""
        if message.message_type == MessageType.AUTH_RESPONSE:
            self._handle_auth_response(message)
        elif message.message_type == MessageType.ELEMENT_ADDED_NOTIFY:
            self._handle_element_added(message)
        elif message.message_type == MessageType.CANVAS_CLEARED_NOTIFY:
            self._handle_canvas_cleared(message)
        elif message.message_type == MessageType.USER_JOINED_NOTIFY:
            self._handle_user_joined(message)
        elif message.message_type == MessageType.USER_LEFT_NOTIFY:
            self._handle_user_left(message)
        elif message.message_type == MessageType.ERROR_NOTIFY:
            self._handle_error(message)
    
    def _handle_auth_response(self, message: Message):
        """处理认证响应"""
        status = message.payload.get("status")
        if status == "success":
            # 加载初始画板状态
            canvas_state = message.payload.get("canvas_state", [])
            for element_data in canvas_state:
                self.canvas_widget.add_element(
                    element_data["element_id"],
                    element_data["user_id"],
                    element_data["type"],
                    element_data["attributes"]
                )
            
            self.logger.info(f"Loaded {len(canvas_state)} elements from server")
        else:
            error_msg = message.payload.get("message", "认证失败")
            show_error(self.root, "认证失败", error_msg)
    
    def _handle_element_added(self, message: Message):
        """处理元素添加通知"""
        payload = message.payload
        self.canvas_widget.add_element(
            payload["element_id"],
            payload["user_id"],
            payload["type"],
            payload["attributes"]
        )
    
    def _handle_canvas_cleared(self, message: Message):
        """处理画板清空通知"""
        self.canvas_widget.clear_canvas()
    
    def _handle_user_joined(self, message: Message):
        """处理用户加入通知"""
        username = message.payload.get("username", "Unknown")
        self.logger.info(f"User joined: {username}")
    
    def _handle_user_left(self, message: Message):
        """处理用户离开通知"""
        user_id = message.payload.get("user_id", "Unknown")
        self.logger.info(f"User left: {user_id}")
    
    def _handle_error(self, message: Message):
        """处理错误通知"""
        error_msg = message.payload.get("message", "未知错误")
        show_error(self.root, "服务器错误", error_msg)
    
    def _on_closing(self):
        """窗口关闭事件"""
        if self.client:
            self.client.disconnect()
        self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    app = MainWindow()
    app.run()


if __name__ == '__main__':
    main()
