"""
工具栏组件
包含绘图工具、颜色选择、画笔设置等控件
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional
from ..protocol.constants import ElementType
from .constants import DEFAULT_COLORS, DEFAULT_THICKNESS


class Toolbar:
    """工具栏组件"""

    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)

        # 回调函数
        self.connect_callback: Optional[Callable[[], None]] = None
        self.disconnect_callback: Optional[Callable[[], None]] = None
        self.tool_changed_callback: Optional[Callable[[str], None]] = None
        self.color_changed_callback: Optional[Callable[[str], None]] = None
        self.thickness_changed_callback: Optional[Callable[[int], None]] = None
        self.clear_canvas_callback: Optional[Callable[[], None]] = None

        # 状态变量
        self.tool_var = tk.StringVar(value=ElementType.LINE)
        self.color_var = tk.StringVar(value=DEFAULT_COLORS[0])
        self.thickness_var = tk.StringVar(value=str(DEFAULT_THICKNESS[0]))

        self._create_widgets()

    def _create_widgets(self):
        """创建工具栏组件"""
        # 连接控制
        self._create_connection_controls()

        # 分隔符
        ttk.Separator(self.frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )

        # 绘图工具
        self._create_drawing_tools()

        # 分隔符
        ttk.Separator(self.frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )

        # 颜色选择
        self._create_color_selector()

        # 分隔符
        ttk.Separator(self.frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )

        # 画笔粗细
        self._create_thickness_selector()

        # 分隔符
        ttk.Separator(self.frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )

        # 操作按钮
        self._create_action_buttons()

    def _create_connection_controls(self):
        """创建连接控制按钮"""
        ttk.Button(self.frame, text="连接", command=self._on_connect).pack(
            side=tk.LEFT, padx=2
        )
        ttk.Button(self.frame, text="断开", command=self._on_disconnect).pack(
            side=tk.LEFT, padx=2
        )

    def _create_drawing_tools(self):
        """创建绘图工具选择"""
        ttk.Label(self.frame, text="工具:").pack(side=tk.LEFT, padx=2)

        tools = [
            (ElementType.LINE, "直线"),
            (ElementType.RECTANGLE, "矩形"),
            (ElementType.CIRCLE, "圆形"),
            (ElementType.FREEHAND, "画笔"),
        ]

        for tool_type, tool_name in tools:
            ttk.Radiobutton(
                self.frame,
                text=tool_name,
                variable=self.tool_var,
                value=tool_type,
                command=self._on_tool_changed,
            ).pack(side=tk.LEFT, padx=2)

    def _create_color_selector(self):
        """创建颜色选择器"""
        ttk.Label(self.frame, text="颜色:").pack(side=tk.LEFT, padx=2)

        color_frame = ttk.Frame(self.frame)
        color_frame.pack(side=tk.LEFT, padx=2)

        # 存储颜色按钮以便后续操作
        self.color_buttons = {}

        # 显示所有8种颜色，分两行
        for i, color in enumerate(DEFAULT_COLORS):
            row = i // 4
            col = i % 4

            btn = tk.Button(
                color_frame,
                bg=color,
                activebackground=color,
                width=3,
                height=1,
                command=lambda c=color: self._on_color_changed(c),
                relief=tk.RAISED,
                bd=2,
                cursor="hand2",
            )
            btn.grid(row=row, column=col, padx=1, pady=1)

            # 为白色添加边框以便可见
            if color == "#FFFFFF":
                btn.config(relief=tk.SOLID, bd=1)

            # 存储按钮引用
            self.color_buttons[color] = btn

            # 添加鼠标悬停效果
            def on_enter(event, button=btn):
                button.config(relief=tk.SUNKEN)

            def on_leave(event, button=btn, original_color=color):
                if original_color == "#FFFFFF":
                    button.config(relief=tk.SOLID)
                else:
                    button.config(relief=tk.RAISED)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

    def _create_thickness_selector(self):
        """创建画笔粗细选择器"""
        ttk.Label(self.frame, text="粗细:").pack(side=tk.LEFT, padx=2)

        thickness_combo = ttk.Combobox(
            self.frame,
            textvariable=self.thickness_var,
            values=[str(t) for t in DEFAULT_THICKNESS],
            width=5,
            state="readonly",
        )
        thickness_combo.pack(side=tk.LEFT, padx=2)
        thickness_combo.bind("<<ComboboxSelected>>", self._on_thickness_changed)

    def _create_action_buttons(self):
        """创建操作按钮"""
        ttk.Button(self.frame, text="清空画板", command=self._on_clear_canvas).pack(
            side=tk.LEFT, padx=2
        )

    def _on_connect(self):
        """连接按钮点击"""
        if self.connect_callback:
            self.connect_callback()

    def _on_disconnect(self):
        """断开按钮点击"""
        if self.disconnect_callback:
            self.disconnect_callback()

    def _on_tool_changed(self):
        """工具改变"""
        if self.tool_changed_callback:
            self.tool_changed_callback(self.tool_var.get())

    def _on_color_changed(self, color: str):
        """颜色改变"""
        self.color_var.set(color)
        if self.color_changed_callback:
            self.color_changed_callback(color)

    def _on_thickness_changed(self, event=None):
        """画笔粗细改变"""
        if self.thickness_changed_callback:
            thickness = int(self.thickness_var.get())
            self.thickness_changed_callback(thickness)

    def _on_clear_canvas(self):
        """清空画板按钮点击"""
        if self.clear_canvas_callback:
            self.clear_canvas_callback()

    def pack(self, **kwargs):
        """打包工具栏"""
        self.frame.pack(**kwargs)

    def set_connect_callback(self, callback: Callable[[], None]):
        """设置连接回调"""
        self.connect_callback = callback

    def set_disconnect_callback(self, callback: Callable[[], None]):
        """设置断开回调"""
        self.disconnect_callback = callback

    def set_tool_changed_callback(self, callback: Callable[[str], None]):
        """设置工具改变回调"""
        self.tool_changed_callback = callback

    def set_color_changed_callback(self, callback: Callable[[str], None]):
        """设置颜色改变回调"""
        self.color_changed_callback = callback

    def set_thickness_changed_callback(self, callback: Callable[[int], None]):
        """设置画笔粗细改变回调"""
        self.thickness_changed_callback = callback

    def set_clear_canvas_callback(self, callback: Callable[[], None]):
        """设置清空画板回调"""
        self.clear_canvas_callback = callback

    def get_current_tool(self) -> str:
        """获取当前工具"""
        return self.tool_var.get()

    def get_current_color(self) -> str:
        """获取当前颜色"""
        return self.color_var.get()

    def get_current_thickness(self) -> int:
        """获取当前画笔粗细"""
        return int(self.thickness_var.get())

    def set_tool(self, tool: str):
        """设置当前工具"""
        self.tool_var.set(tool)

    def set_color(self, color: str):
        """设置当前颜色"""
        self.color_var.set(color)

    def set_thickness(self, thickness: int):
        """设置当前画笔粗细"""
        self.thickness_var.set(str(thickness))
