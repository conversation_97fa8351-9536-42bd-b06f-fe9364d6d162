"""
简单加密功能
实现密码哈希和基础加密功能
"""

import hashlib
import secrets
from typing import <PERSON>ple


def hash_password(password: str, salt: str = None) -> Tuple[str, str]:
    if salt is None:
        salt = secrets.token_hex(16)

    # 使用SHA256进行哈希
    password_salt = (password + salt).encode("utf-8")
    hashed = hashlib.sha256(password_salt).hexdigest()

    return hashed, salt


def verify_password(password: str, hashed_password: str, salt: str) -> bool:
    computed_hash, _ = hash_password(password, salt)
    return computed_hash == hashed_password


def simple_hash(text: str) -> str:
    return hashlib.sha256(text.encode("utf-8")).hexdigest()


# 简单的XOR加密（仅用于演示，实际应用中应使用更强的加密）
def xor_encrypt(data: bytes, key: bytes) -> bytes:
    if not key:
        return data

    result = bytearray()
    key_len = len(key)

    for i, byte in enumerate(data):
        result.append(byte ^ key[i % key_len])

    return bytes(result)


def xor_decrypt(data: bytes, key: bytes) -> bytes:
    return xor_encrypt(data, key)


def generate_session_key() -> bytes:
    return secrets.token_bytes(32)
