"""
客户端连接处理器
处理单个客户端的连接和消息
"""

import socket
import threading
import time
from typing import Optional, Callable
from ..protocol.message import Message, MessageBuilder
from ..protocol.constants import MessageType, AuthStatus, ErrorCode, HEADER_LENGTH
from ..protocol.crypto import verify_password
from ..utils.logger import get_logger


class ClientHandler:
    """客户端连接处理器"""

    def __init__(
        self,
        client_socket: socket.socket,
        client_address: tuple,
        canvas_state,
        user_manager,
        broadcast_callback: Callable,
        user_database=None,
    ):
        self.socket = client_socket
        self.address = client_address
        self.canvas_state = canvas_state
        self.user_manager = user_manager
        self.broadcast_callback = broadcast_callback
        self.user_database = user_database

        self.user_id: Optional[str] = None
        self.username: Optional[str] = None
        self.authenticated = False
        self.running = False

        self.logger = get_logger(
            f"ClientHandler-{client_address[0]}:{client_address[1]}"
        )

        # 设置socket超时
        self.socket.settimeout(60.0)

    def start(self):
        """启动客户端处理"""
        self.running = True
        self.logger.info(f"Client connected from {self.address}")

        try:
            self._handle_client()
        except Exception as e:
            self.logger.error(f"Error handling client: {e}")
        finally:
            self.cleanup()

    def _handle_client(self):
        """处理客户端消息循环"""
        while self.running:
            try:
                # 接收消息
                message = self._receive_message()
                if message is None:
                    break

                self.logger.debug(f"Received message: {message}")

                # 处理消息
                self._process_message(message)

            except socket.timeout:
                # 检查心跳
                if self.authenticated:
                    self.logger.warning("Client heartbeat timeout")
                    break
            except ConnectionResetError:
                self.logger.info("Client disconnected")
                break
            except Exception as e:
                self.logger.error(f"Error in message handling: {e}")
                break

    def _receive_message(self) -> Optional[Message]:
        """接收完整消息"""
        try:
            # 接收消息头
            header_data = self._receive_exact(HEADER_LENGTH)
            if not header_data:
                return None

            # 解析消息头获取payload长度
            _, _, payload_length, _ = Message.decode_header(header_data)

            # 接收payload
            payload_data = b""
            if payload_length > 0:
                payload_data = self._receive_exact(payload_length)
                if not payload_data:
                    return None

            # 解码完整消息
            full_data = header_data + payload_data
            return Message.decode(full_data)

        except Exception as e:
            self.logger.error(f"Error receiving message: {e}")
            return None

    def _receive_exact(self, length: int) -> Optional[bytes]:
        """接收指定长度的数据"""
        data = b""
        while len(data) < length:
            try:
                chunk = self.socket.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.error:
                return None
        return data

    def _process_message(self, message: Message):
        """处理接收到的消息"""
        if message.message_type == MessageType.AUTH_REQUEST:
            self._handle_auth_request(message)
        elif message.message_type == MessageType.REGISTER_REQUEST:
            self._handle_register_request(message)
        elif not self.authenticated:
            # 未认证的客户端只能发送认证请求或注册请求
            self._send_error(ErrorCode.UNAUTHORIZED, "Authentication required")
        elif message.message_type == MessageType.ADD_ELEMENT_REQUEST:
            self._handle_add_element_request(message)
        elif message.message_type == MessageType.CLEAR_CANVAS_REQUEST:
            self._handle_clear_canvas_request(message)
        elif message.message_type == MessageType.HEARTBEAT_REQUEST:
            self._handle_heartbeat_request(message)
        else:
            self._send_error(ErrorCode.INVALID_REQUEST, "Unknown message type")

    def _handle_auth_request(self, message: Message):
        """处理认证请求"""
        try:
            username = message.payload.get("username")
            password_hash = message.payload.get("password_hash")

            if not username or not password_hash:
                self._send_auth_response(
                    AuthStatus.FAILURE, message="Username and password required"
                )
                return

            # 检查是否有用户数据库
            if self.user_database:
                # 使用数据库认证
                if self.user_database.verify_user(username, password_hash):
                    self._authenticate_user(username)
                else:
                    self._send_auth_response(
                        AuthStatus.FAILURE, message="Invalid credentials"
                    )
            else:
                # 简单认证：接受任何用户名，密码为用户名的哈希
                from ..protocol.crypto import simple_hash

                expected_hash = simple_hash(username)

                if password_hash == expected_hash:
                    self._authenticate_user(username)
                else:
                    self._send_auth_response(
                        AuthStatus.FAILURE, message="Invalid credentials"
                    )

        except Exception as e:
            self.logger.error(f"Error in authentication: {e}")
            self._send_auth_response(AuthStatus.FAILURE, message="Authentication error")

    def _handle_register_request(self, message: Message):
        """处理注册请求"""
        try:
            username = message.payload.get("username")
            password_hash = message.payload.get("password_hash")

            if not username or not password_hash:
                self._send_register_response(False, "Username and password required")
                return

            if not self.user_database:
                self._send_register_response(False, "Registration not supported")
                return

            # 尝试注册用户
            success, message_text = self.user_database.register_user(
                username, password_hash
            )
            self._send_register_response(success, message_text)

            if success:
                self.logger.info(f"User {username} registered successfully")

        except Exception as e:
            self.logger.error(f"Error in registration: {e}")
            self._send_register_response(False, "Registration error")

    def _authenticate_user(self, username: str):
        """认证用户成功的通用处理"""
        # 认证成功
        self.user_id = self.user_manager.add_user(username, self)
        self.username = username
        self.authenticated = True

        # 发送认证成功响应，包含当前画板状态
        canvas_state = self.canvas_state.get_all_elements()
        self._send_auth_response(
            AuthStatus.SUCCESS,
            self.user_id,
            "Authentication successful",
            canvas_state,
        )

        # 广播用户加入通知
        join_message = MessageBuilder.user_joined_notify(self.user_id, username)
        self.broadcast_callback(join_message, exclude=self)

        self.logger.info(f"User {username} authenticated successfully")

    def _handle_add_element_request(self, message: Message):
        """处理添加元素请求"""
        try:
            element_type = message.payload.get("type")
            attributes = message.payload.get("attributes", {})

            if not element_type:
                self._send_error(ErrorCode.INVALID_REQUEST, "Element type required")
                return

            # 添加元素到画板状态
            element_id = self.canvas_state.add_element(
                self.user_id, element_type, attributes
            )

            # 广播元素添加通知
            notify_message = MessageBuilder.element_added_notify(
                element_id, self.user_id, element_type, attributes
            )
            self.broadcast_callback(notify_message)

        except Exception as e:
            self.logger.error(f"Error adding element: {e}")
            self._send_error(ErrorCode.INTERNAL_ERROR, "Failed to add element")

    def _handle_clear_canvas_request(self, message: Message):
        """处理清空画板请求"""
        try:
            # 清空画板
            self.canvas_state.clear_canvas()

            # 广播画板清空通知
            notify_message = MessageBuilder.canvas_cleared_notify()
            self.broadcast_callback(notify_message)

        except Exception as e:
            self.logger.error(f"Error clearing canvas: {e}")
            self._send_error(ErrorCode.INTERNAL_ERROR, "Failed to clear canvas")

    def _handle_heartbeat_request(self, message: Message):
        """处理心跳请求"""
        response = MessageBuilder.heartbeat_response()
        self.send_message(response)

    def _send_auth_response(
        self,
        status: str,
        user_id: str = None,
        message: str = "",
        canvas_state: list = None,
    ):
        """发送认证响应"""
        response = MessageBuilder.auth_response(status, user_id, message, canvas_state)
        self.send_message(response)

    def _send_register_response(self, success: bool, message: str):
        """发送注册响应"""
        response = MessageBuilder.register_response(success, message)
        self.send_message(response)

    def _send_error(self, code: int, message: str):
        """发送错误消息"""
        error_message = MessageBuilder.error_notify(code, message)
        self.send_message(error_message)

    def send_message(self, message: Message) -> bool:
        """发送消息到客户端"""
        try:
            data = message.encode()
            self.socket.sendall(data)
            self.logger.debug(f"Sent message: {message}")
            return True
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        self.running = False

        if self.authenticated and self.user_id:
            # 从用户管理器中移除用户
            self.user_manager.remove_user(self.user_id)

            # 广播用户离开通知
            leave_message = MessageBuilder.user_left_notify(self.user_id)
            self.broadcast_callback(leave_message, exclude=self)

            self.logger.info(f"User {self.username} disconnected")

        try:
            self.socket.close()
        except:
            pass

        self.logger.info(f"Client {self.address} disconnected")
