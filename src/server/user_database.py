"""
用户数据库管理
简单的用户注册和认证系统
"""

import threading
import json
import os
from typing import Dict, Optional, Tuple
from ..protocol.crypto import hash_password, verify_password
from ..utils.logger import get_logger


class UserDatabase:
    """用户数据库管理器"""

    def __init__(self, db_file: str = "users.json"):
        self.db_file = db_file
        self.users: Dict[str, Dict] = {}
        self.lock = threading.RLock()
        self.logger = get_logger("UserDatabase")

        # 加载用户数据
        self._load_users()

    def _load_users(self):
        """从文件加载用户数据"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, "r", encoding="utf-8") as f:
                    self.users = json.load(f)
                self.logger.info(f"Loaded {len(self.users)} users from {self.db_file}")
            else:
                self.logger.info("No existing user database found, starting fresh")
        except Exception as e:
            self.logger.error(f"Failed to load user database: {e}")
            self.users = {}

    def _save_users(self):
        """保存用户数据到文件"""
        try:
            with open(self.db_file, "w", encoding="utf-8") as f:
                json.dump(self.users, f, indent=2, ensure_ascii=False)
            self.logger.debug(f"Saved {len(self.users)} users to {self.db_file}")
        except Exception as e:
            self.logger.error(f"Failed to save user database: {e}")

    def register_user(self, username: str, password_hash: str) -> Tuple[bool, str]:
        """
        注册新用户

        Args:
            username: 用户名
            password_hash: 密码哈希值（客户端已经哈希过的）

        Returns:
            (success, message): 注册结果和消息
        """
        with self.lock:
            # 验证用户名
            if not username or len(username.strip()) < 2:
                return False, "用户名长度至少2个字符"

            username = username.strip()

            # 检查用户名是否已存在
            if username in self.users:
                return False, "用户名已存在"

            # 验证密码哈希
            if not password_hash or len(password_hash) < 10:
                return False, "密码哈希无效"

            try:
                # 直接使用客户端提供的密码哈希
                # 创建用户记录
                user_record = {
                    "username": username,
                    "password_hash": password_hash,
                    "salt": None,  # 使用simple_hash时不需要salt
                    "created_at": self._get_timestamp(),
                    "last_login": None,
                }

                # 保存用户
                self.users[username] = user_record
                self._save_users()

                self.logger.info(f"User '{username}' registered successfully")
                return True, "注册成功"

            except Exception as e:
                self.logger.error(f"Failed to register user '{username}': {e}")
                return False, "注册失败，服务器错误"

    def authenticate_user(self, username: str, password: str) -> Tuple[bool, str]:
        """
        用户认证

        Args:
            username: 用户名
            password: 密码

        Returns:
            (success, message): 认证结果和消息
        """
        with self.lock:
            if not username or not password:
                return False, "用户名和密码不能为空"

            username = username.strip()

            # 检查用户是否存在
            if username not in self.users:
                # 简化版认证：如果用户不存在，尝试简单认证（密码=用户名的哈希）
                from ..protocol.crypto import simple_hash

                expected_hash = simple_hash(username)
                if simple_hash(password) == expected_hash:
                    self.logger.info(
                        f"User '{username}' authenticated with simple auth"
                    )
                    return True, "认证成功（简单模式）"
                else:
                    return False, "用户不存在或密码错误"

            try:
                user_record = self.users[username]

                # 验证密码
                if verify_password(
                    password, user_record["password_hash"], user_record["salt"]
                ):
                    # 更新最后登录时间
                    user_record["last_login"] = self._get_timestamp()
                    self._save_users()

                    self.logger.info(f"User '{username}' authenticated successfully")
                    return True, "认证成功"
                else:
                    self.logger.warning(
                        f"Failed authentication attempt for user '{username}'"
                    )
                    return False, "密码错误"

            except Exception as e:
                self.logger.error(f"Authentication error for user '{username}': {e}")
                return False, "认证失败，服务器错误"

    def verify_user(self, username: str, password_hash: str) -> bool:
        """
        验证用户（使用密码哈希）

        Args:
            username: 用户名
            password_hash: 密码哈希值

        Returns:
            bool: 验证是否成功
        """
        with self.lock:
            if not username or not password_hash:
                return False

            username = username.strip()

            # 检查用户是否存在
            if username not in self.users:
                # 简化版认证：如果用户不存在，尝试简单认证（密码=用户名的哈希）
                from ..protocol.crypto import simple_hash

                expected_hash = simple_hash(username)
                if password_hash == expected_hash:
                    self.logger.info(f"User '{username}' verified with simple auth")
                    return True
                else:
                    return False

            try:
                user_record = self.users[username]

                # 直接比较哈希值（现在注册和验证都使用相同的哈希方法）
                stored_hash = user_record["password_hash"]
                if password_hash == stored_hash:
                    # 更新最后登录时间
                    user_record["last_login"] = self._get_timestamp()
                    self._save_users()

                    self.logger.info(f"User '{username}' verified successfully")
                    return True
                else:
                    self.logger.warning(
                        f"Failed verification attempt for user '{username}'"
                    )
                    return False

            except Exception as e:
                self.logger.error(f"Verification error for user '{username}': {e}")
                return False

    def user_exists(self, username: str) -> bool:
        """检查用户是否存在"""
        with self.lock:
            return username.strip() in self.users

    def get_user_count(self) -> int:
        """获取用户总数"""
        with self.lock:
            return len(self.users)

    def get_user_info(self, username: str) -> Optional[Dict]:
        """获取用户信息（不包含密码）"""
        with self.lock:
            if username in self.users:
                user_record = self.users[username].copy()
                # 移除敏感信息
                user_record.pop("password_hash", None)
                user_record.pop("salt", None)
                return user_record
            return None

    def delete_user(self, username: str) -> bool:
        """删除用户"""
        with self.lock:
            if username in self.users:
                del self.users[username]
                self._save_users()
                self.logger.info(f"User '{username}' deleted")
                return True
            return False

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        import datetime

        return datetime.datetime.now().isoformat()

    def get_stats(self) -> Dict:
        """获取数据库统计信息"""
        with self.lock:
            return {
                "total_users": len(self.users),
                "database_file": self.db_file,
                "file_exists": os.path.exists(self.db_file),
            }

    def __str__(self) -> str:
        return f"UserDatabase(users={len(self.users)}, file={self.db_file})"
