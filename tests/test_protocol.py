"""
协议模块测试
"""

import pytest
import json
from src.protocol.message import Message, MessageBuilder
from src.protocol.constants import MessageType, ElementType
from src.protocol.crypto import hash_password, verify_password, simple_hash


class TestMessage:
    """消息类测试"""
    
    def test_message_encode_decode(self):
        """测试消息编码和解码"""
        # 创建测试消息
        payload = {"test": "data", "number": 123}
        message = Message(MessageType.AUTH_REQUEST, payload)
        
        # 编码
        encoded = message.encode()
        assert isinstance(encoded, bytes)
        assert len(encoded) > 8  # 至少包含消息头
        
        # 解码
        decoded = Message.decode(encoded)
        assert decoded.message_type == MessageType.AUTH_REQUEST
        assert decoded.payload == payload
        assert decoded.version == message.version
    
    def test_empty_payload(self):
        """测试空payload"""
        message = Message(MessageType.HEARTBEAT_REQUEST)
        encoded = message.encode()
        decoded = Message.decode(encoded)
        
        assert decoded.message_type == MessageType.HEARTBEAT_REQUEST
        assert decoded.payload == {}
    
    def test_large_payload(self):
        """测试大payload"""
        large_data = {"data": "x" * 10000}
        message = Message(MessageType.ADD_ELEMENT_REQUEST, large_data)
        
        encoded = message.encode()
        decoded = Message.decode(encoded)
        
        assert decoded.payload == large_data
    
    def test_invalid_data(self):
        """测试无效数据"""
        with pytest.raises(ValueError):
            Message.decode(b"invalid")
        
        with pytest.raises(ValueError):
            Message.decode(b"12345")  # 太短


class TestMessageBuilder:
    """消息构建器测试"""
    
    def test_auth_request(self):
        """测试认证请求消息"""
        message = MessageBuilder.auth_request("testuser", "hash123")
        
        assert message.message_type == MessageType.AUTH_REQUEST
        assert message.payload["username"] == "testuser"
        assert message.payload["password_hash"] == "hash123"
    
    def test_auth_response(self):
        """测试认证响应消息"""
        canvas_state = [{"element_id": "1", "type": "line"}]
        message = MessageBuilder.auth_response("success", "user1", "OK", canvas_state)
        
        assert message.message_type == MessageType.AUTH_RESPONSE
        assert message.payload["status"] == "success"
        assert message.payload["user_id"] == "user1"
        assert message.payload["canvas_state"] == canvas_state
    
    def test_add_element_request(self):
        """测试添加元素请求消息"""
        attributes = {"startX": 10, "startY": 20, "endX": 100, "endY": 200}
        message = MessageBuilder.add_element_request(ElementType.LINE, attributes)
        
        assert message.message_type == MessageType.ADD_ELEMENT_REQUEST
        assert message.payload["type"] == ElementType.LINE
        assert message.payload["attributes"] == attributes
    
    def test_element_added_notify(self):
        """测试元素添加通知消息"""
        attributes = {"color": "#FF0000"}
        message = MessageBuilder.element_added_notify("elem1", "user1", ElementType.CIRCLE, attributes)
        
        assert message.message_type == MessageType.ELEMENT_ADDED_NOTIFY
        assert message.payload["element_id"] == "elem1"
        assert message.payload["user_id"] == "user1"
        assert message.payload["type"] == ElementType.CIRCLE
        assert message.payload["attributes"] == attributes


class TestCrypto:
    """加密功能测试"""
    
    def test_hash_password(self):
        """测试密码哈希"""
        password = "testpassword"
        hashed, salt = hash_password(password)
        
        assert isinstance(hashed, str)
        assert isinstance(salt, str)
        assert len(hashed) == 64  # SHA256 hex length
        assert len(salt) == 32   # 16 bytes hex
    
    def test_verify_password(self):
        """测试密码验证"""
        password = "testpassword"
        hashed, salt = hash_password(password)
        
        # 正确密码
        assert verify_password(password, hashed, salt) == True
        
        # 错误密码
        assert verify_password("wrongpassword", hashed, salt) == False
    
    def test_simple_hash(self):
        """测试简单哈希"""
        text = "test"
        hash1 = simple_hash(text)
        hash2 = simple_hash(text)
        
        assert hash1 == hash2  # 相同输入产生相同哈希
        assert len(hash1) == 64  # SHA256 hex length
        
        # 不同输入产生不同哈希
        hash3 = simple_hash("different")
        assert hash1 != hash3


if __name__ == '__main__':
    pytest.main([__file__])
